1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.drivers_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <!-- Needs to be explicitly declared on Android R+ -->
30        <package android:name="com.google.android.apps.maps" />
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
31    </queries>
32
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
33-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
33-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
34    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
34-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
34-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[:connectivity_plus] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
35-->[:connectivity_plus] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
36    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
36-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
37-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
38    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
38-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
38-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
39-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
39-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
40
41    <uses-feature
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
42        android:glEsVersion="0x00020000"
42-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
43        android:required="true" />
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
44
45    <permission
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
46        android:name="com.example.drivers_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.example.drivers_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
50
51    <application
52        android:name="android.app.Application"
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:debuggable="true"
55        android:extractNativeLibs="true"
56        android:icon="@mipmap/ic_launcher"
57        android:label="drivers_app" >
58        <activity
59            android:name="com.example.drivers_app.MainActivity"
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61            android:exported="true"
62            android:hardwareAccelerated="true"
63            android:launchMode="singleTop"
64            android:taskAffinity=""
65            android:theme="@style/LaunchTheme"
66            android:windowSoftInputMode="adjustResize" >
67
68            <!--
69                 Specifies an Android theme to apply to this Activity as soon as
70                 the Android process has started. This theme is visible to the user
71                 while the Flutter UI initializes. After that, this theme continues
72                 to determine the Window background behind the Flutter UI.
73            -->
74            <meta-data
75                android:name="io.flutter.embedding.android.NormalTheme"
76                android:resource="@style/NormalTheme" />
77
78            <intent-filter>
79                <action android:name="android.intent.action.MAIN" />
80
81                <category android:name="android.intent.category.LAUNCHER" />
82            </intent-filter>
83        </activity>
84        <!--
85             Don't delete the meta-data below.
86             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
87        -->
88        <meta-data
89            android:name="flutterEmbedding"
90            android:value="2" />
91
92        <service
92-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
93            android:name="com.lyokone.location.FlutterLocationService"
93-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
94            android:enabled="true"
94-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
95            android:exported="false"
95-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
96            android:foregroundServiceType="location" />
96-->[:location] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
97        <service
97-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
98            android:name="com.baseflow.geolocator.GeolocatorLocationService"
98-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
99            android:enabled="true"
99-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
100            android:exported="false"
100-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
101            android:foregroundServiceType="location" />
101-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
102        <service
102-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
103            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
103-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
104            android:exported="false"
104-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
105            android:permission="android.permission.BIND_JOB_SERVICE" />
105-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
106        <service
106-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
107            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
107-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
108            android:exported="false" >
108-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
109            <intent-filter>
109-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
110                <action android:name="com.google.firebase.MESSAGING_EVENT" />
110-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
110-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
111            </intent-filter>
112        </service>
113
114        <receiver
114-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
115            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
115-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
116            android:exported="true"
116-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
117            android:permission="com.google.android.c2dm.permission.SEND" >
117-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
118            <intent-filter>
118-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
119                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
119-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
119-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
120            </intent-filter>
121        </receiver>
122
123        <service
123-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
124            android:name="com.google.firebase.components.ComponentDiscoveryService"
124-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
125            android:directBootAware="true"
125-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
126            android:exported="false" >
126-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:31:13-37
127            <meta-data
127-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
128                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
128-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
130            <meta-data
130-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
131                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
131-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
133            <meta-data
133-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
134                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
134-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
136            <meta-data
136-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
137                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
137-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
139            <meta-data
139-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
140                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
140-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
142            <meta-data
142-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
143                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
143-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
145            <meta-data
145-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
146                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
146-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
148            <meta-data
148-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
149                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
149-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
151            <meta-data
151-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
152                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
152-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
154            <meta-data
154-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
155                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
155-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
157        </service>
158
159        <provider
159-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
160            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
160-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
161            android:authorities="com.example.drivers_app.flutterfirebasemessaginginitprovider"
161-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
162            android:exported="false"
162-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
163            android:initOrder="99" />
163-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
164        <provider
164-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
165            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
165-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
166            android:authorities="com.example.drivers_app.flutter.image_provider"
166-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
167            android:exported="false"
167-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
168            android:grantUriPermissions="true" >
168-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
169            <meta-data
169-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
170                android:name="android.support.FILE_PROVIDER_PATHS"
170-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
171                android:resource="@xml/flutter_image_picker_file_paths" />
171-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
172        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
173        <service
173-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
174            android:name="com.google.android.gms.metadata.ModuleDependencies"
174-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
175            android:enabled="false"
175-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
176            android:exported="false" >
176-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
177            <intent-filter>
177-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
178                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
178-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
178-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
179            </intent-filter>
180
181            <meta-data
181-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
182                android:name="photopicker_activity:0:required"
182-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
183                android:value="" />
183-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
184        </service>
185
186        <activity
186-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
187            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
187-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
188            android:exported="false"
188-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
189            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
189-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
190        <uses-library
190-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
191            android:name="org.apache.http.legacy"
191-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
192            android:required="false" />
192-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
193
194        <receiver
194-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
195            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
195-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
196            android:exported="true"
196-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
197            android:permission="com.google.android.c2dm.permission.SEND" >
197-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
198            <intent-filter>
198-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
199-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
200            </intent-filter>
201
202            <meta-data
202-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
203                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
203-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
204                android:value="true" />
204-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
205        </receiver>
206        <!--
207             FirebaseMessagingService performs security checks at runtime,
208             but set to not exported to explicitly avoid allowing another app to call it.
209        -->
210        <service
210-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
211            android:name="com.google.firebase.messaging.FirebaseMessagingService"
211-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
212            android:directBootAware="true"
212-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
213            android:exported="false" >
213-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
214            <intent-filter android:priority="-500" >
214-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
215                <action android:name="com.google.firebase.MESSAGING_EVENT" />
215-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
215-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\drivers_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
216            </intent-filter>
217        </service>
218
219        <provider
219-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
220            android:name="com.google.firebase.provider.FirebaseInitProvider"
220-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
221            android:authorities="com.example.drivers_app.firebaseinitprovider"
221-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
222            android:directBootAware="true"
222-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
223            android:exported="false"
223-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
224            android:initOrder="100" />
224-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
225
226        <activity
226-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
227            android:name="com.google.android.gms.common.api.GoogleApiActivity"
227-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
228            android:exported="false"
228-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
229            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
229-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
230
231        <uses-library
231-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
232            android:name="androidx.window.extensions"
232-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
233            android:required="false" />
233-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
234        <uses-library
234-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
235            android:name="androidx.window.sidecar"
235-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
236            android:required="false" />
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
237
238        <meta-data
238-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
239            android:name="com.google.android.gms.version"
239-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
240            android:value="@integer/google_play_services_version" />
240-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
241
242        <provider
242-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
243            android:name="androidx.startup.InitializationProvider"
243-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
244            android:authorities="com.example.drivers_app.androidx-startup"
244-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
245            android:exported="false" >
245-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
246            <meta-data
246-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
247                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
247-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
248                android:value="androidx.startup" />
248-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
249            <meta-data
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
250                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
251                android:value="androidx.startup" />
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
252        </provider>
253
254        <receiver
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
255            android:name="androidx.profileinstaller.ProfileInstallReceiver"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
256            android:directBootAware="false"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
257            android:enabled="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
258            android:exported="true"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
259            android:permission="android.permission.DUMP" >
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
261                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
264                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
267                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
270                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
271            </intent-filter>
272        </receiver>
273
274        <service
274-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
275            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
275-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
276            android:exported="false" >
276-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
277            <meta-data
277-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
278                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
278-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
279                android:value="cct" />
279-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
280        </service>
281        <service
281-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
282            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
282-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
283            android:exported="false"
283-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
284            android:permission="android.permission.BIND_JOB_SERVICE" >
284-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
285        </service>
286
287        <receiver
287-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
288            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
288-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
289            android:exported="false" />
289-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
290    </application>
291
292</manifest>
