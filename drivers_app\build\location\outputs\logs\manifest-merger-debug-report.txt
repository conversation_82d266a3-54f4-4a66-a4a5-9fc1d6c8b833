-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:1:1-14:12
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:1:1-14:12
	package
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:3:5-81
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:3:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:4:5-79
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:4:22-76
application
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:6:5-12:19
service#com.lyokone.location.FlutterLocationService
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:7:9-11:53
	android:enabled
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:8:13-35
	android:exported
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:9:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:10:13-53
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml:11:13-51
uses-sdk
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\location-5.0.3\android\src\main\AndroidManifest.xml
