name: drivers_app
description: A delivery driver application for restaurant deliveries.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  http: ^0.13.5
  provider: ^6.0.5
  shared_preferences: ^2.0.15
  intl: ^0.18.1
  url_launcher: ^6.1.7
  marquee: ^2.2.3
  google_maps_flutter: ^2.2.5
  location: ^5.0.3
  flutter_polyline_points: ^1.0.0
  firebase_core: ^2.13.0
  firebase_messaging: ^14.6.1
  flutter_local_notifications: ^17.1.2
  image_picker: ^1.0.7
  cached_network_image: ^3.2.3
  connectivity_plus: ^5.0.2
  geolocator: ^9.0.2
  geocoding: ^2.1.0
  flutter_svg: ^2.0.5
  shimmer: ^2.0.0
  dio: ^5.1.2
  flutter_config: ^2.0.0
  path_provider: ^2.0.15

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

dependency_overrides:
  fading_edge_scrollview: ^4.1.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/
    - assets/fonts/Alexandria-Regular.ttf
    - assets/fonts/Cairo-Light.ttf

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Alx
      fonts:
        - asset: assets/fonts/Alexandria-Regular.ttf
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Light.ttf
    - family: Lateef
      fonts:
        - asset: assets/fonts/Lateef-Regular.ttf
          weight: 700
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
